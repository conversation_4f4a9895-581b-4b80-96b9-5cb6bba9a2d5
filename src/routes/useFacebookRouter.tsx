import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { FbAuthProvider } from '@/pages/Facebook/context/FbAuthContext';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const FacebookPage = lazy(() => import('@/pages/Facebook'));

const useFacebookRouter = (): RouteObject[] => [
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.facebook['']}`,
    element: (
      <SuspenseWrapper
        component={
          <FbAuthProvider>
            <FacebookPage />
          </FbAuthProvider>
        }
      />
    ),
  },
];

export default useFacebookRouter;
