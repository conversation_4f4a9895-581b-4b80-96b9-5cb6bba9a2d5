import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const GetPhonePage = lazy(() => import('@/pages/ContactList/GetPhone'));
const ContactListPage = lazy(() => import('@/pages/ContactListContainer'));
const SegmentList = lazy(() => import('@/pages/Segment'));
const SegmentDetail = lazy(() => import('@/pages/Segment/DetailSegment'));
const ContactDetail = lazy(() => import('@/pages/Detail'));

const useContactRouter = (): RouteObject[] => [
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.contact['']}`,
    element: <SuspenseWrapper component={<ContactListPage />} />,
  },
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE['get-phone-number']}`,
    element: <SuspenseWrapper component={<GetPhonePage />} />,
  },
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.contact.segment}`,
    element: <SuspenseWrapper component={<SegmentList />} />,
  },
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.contact.segmentDetail}/:id`,
    element: <SuspenseWrapper component={<SegmentDetail />} />,
  },
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.contact.detail}/:id`,
    element: <SuspenseWrapper component={<ContactDetail />} />,
  },
];

export default useContactRouter;
