import React from 'react';
import { CRM360Icon } from '@/assets/CRM360Icon';
import { Link, useLocation } from 'react-router-dom';
import { RiArrowRightSLine } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { navLinkConfig, TNavLinkConfig, TNavLinkConfigItem } from '@/utils/helper';
import { ROOT_PATH, ROOT_ROUTE, TIKTOK_ADS_ROUTER } from '@/constants/router';

type TBreadcrumb = {
  endUrl?: string;
  title?: string;
};

interface BreadcrumbItem {
  id: string | number;
  url: string;
  title: string;
  isHideArrow: boolean;
}

const Breadcrumb: React.FC<TBreadcrumb> = ({ endUrl, title }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const navLinks = navLinkConfig();

  const routes = {
    contact: `${ROOT_PATH}/${ROOT_ROUTE.contact['']}`,
    facebook: `${ROOT_PATH}/${ROOT_ROUTE.facebook['']}`,
    tiktok: `${ROOT_PATH}/${ROOT_ROUTE.tiktok['']}`,
  };

  const isCurrentRoute = (path: string): boolean => {
    return location.pathname.includes(path);
  };

  const isTiktokFailedPage = (): boolean => {
    // Handle both absolute paths and paths with ROOT_PATH
    const authFailedPath = `${ROOT_PATH}${TIKTOK_ADS_ROUTER.AUTHORIZE_FAILED}`;
    const permissionFailedPath = `${ROOT_PATH}${TIKTOK_ADS_ROUTER.PERMISSION_FAILED}`;

    return location.pathname === TIKTOK_ADS_ROUTER.AUTHORIZE_FAILED ||
           location.pathname === TIKTOK_ADS_ROUTER.PERMISSION_FAILED ||
           location.pathname === authFailedPath ||
           location.pathname === permissionFailedPath;
  };

  const shouldHideTiktokArrow = (): boolean => {
    if (isTiktokFailedPage()) return true;
    if (location.pathname === `${ROOT_PATH}/tiktok-ads`) return true;
    if (location.pathname === routes.tiktok) return true;
    return false;
  };

  const shouldHideContactArrow = (): boolean => {
    return location.pathname === routes.contact;
  };

  const mainRoutes: BreadcrumbItem[] = [
    {
      id: 'contact',
      url: routes.contact,
      title: t('contactList.titlePageBreadcrumb'),
      isHideArrow: shouldHideContactArrow(),
    },
    {
      id: 'facebook',
      url: routes.facebook,
      title: t('common.facebookTitlePageBreadcrumb'),
      isHideArrow: location.pathname === routes.facebook,
    },
    {
      id: 'tiktok',
      url: routes.tiktok,
      title: t('common.tiktokTitlePageBreadcrumb'),
      isHideArrow: shouldHideTiktokArrow(),
    },
  ];

  const getVisibleRoutes = (): BreadcrumbItem[] => {
    return mainRoutes.filter((route) => {
      if (route.id === 'tiktok') {
        // Show TikTok breadcrumb for: /tiktok-ads route, any tiktok sub-routes, or failed pages
        return isCurrentRoute(route.url) ||
               location.pathname === `${ROOT_PATH}/tiktok-ads` ||
               isTiktokFailedPage();
      }
      return isCurrentRoute(route.url);
    });
  };

  const getNavLinkItems = (): BreadcrumbItem[] => {
    if (isTiktokFailedPage()) return [];

    const items: BreadcrumbItem[] = [];

    if (location.pathname.includes('/contact/segment')) {
      items.push({
        id: 'segment',
        url: `${ROOT_PATH}/${ROOT_ROUTE.contact.segment}`,
        title: t('segment.title'),
        isHideArrow: location.pathname === `${ROOT_PATH}/${ROOT_ROUTE.contact.segment}`,
      });
    }

    navLinks.forEach((navLink: TNavLinkConfig) => {
      navLink.item
        .filter((item: TNavLinkConfigItem) => {
          if (item.id === 'segmentList') return false;
          return isCurrentRoute(item.url);
        })
        .forEach((item: TNavLinkConfigItem) => {
          if (!item.isHideTitle) {
            items.push({
              id: item.id,
              url: item.url,
              title: item.title,
              isHideArrow: !location.pathname.includes('detail'),
            });
          }
        });
    });

    return items;
  };

  const visibleRoutes = getVisibleRoutes();
  const getDynamicDetailItems = (): BreadcrumbItem[] => {
    const items: BreadcrumbItem[] = [];

    if (location.pathname.includes('/contact-detail/')) {
      items.push({
        id: 'contact-detail',
        url: location.pathname,
        title: title || 'Contact Detail',
        isHideArrow: true,
      });
    }

    if (location.pathname.includes('/contact/segment/detail/')) {
      items.push({
        id: 'segment-detail',
        url: location.pathname,
        title: title || 'Segment Detail',
        isHideArrow: true,
      });
    }

    return items;
  };

  const navLinkItems = getNavLinkItems();
  const dynamicDetailItems = getDynamicDetailItems();

  return (
    <div className="pb-4 flex items-center gap-1">
      <CRM360Icon />
      <div className="flex items-center gap-1">
        <span className="text-xs font-normal text-secondary">CRM 360</span>
        <RiArrowRightSLine />
      </div>

      {visibleRoutes.map((route) => (
        <ItemBreadcrumb
          key={route.id}
          url={route.url}
          title={route.title}
          isHideArrow={route.isHideArrow}
        />
      ))}

      {navLinkItems.map((item) => (
        <ItemBreadcrumb
          key={item.id}
          url={item.url}
          title={item.title}
          isHideArrow={item.isHideArrow}
        />
      ))}

      {dynamicDetailItems.map((item) => (
        <ItemBreadcrumb
          key={item.id}
          url={item.url}
          title={item.title}
          isHideArrow={item.isHideArrow}
        />
      ))}

      {!isTiktokFailedPage() &&
       !location.pathname.includes('/contact-detail/') &&
       !location.pathname.includes('/contact/segment/detail/') &&
       (endUrl || title) && (
        <ItemBreadcrumb
          key={endUrl || 'custom-end'}
          url={endUrl ?? ''}
          title={title ?? ''}
          isHideArrow={true}
        />
      )}
    </div>
  );
};

export default Breadcrumb;

interface ItemBreadcrumbProps {
  url: string;
  title: string;
  isHideArrow: boolean;
}

const ItemBreadcrumb: React.FC<ItemBreadcrumbProps> = ({ url, title, isHideArrow }) => {
  return (
    <Link to={url} className="flex items-center gap-1">
      <span className="text-xs font-normal text-secondary">{title}</span>
      {!isHideArrow && <RiArrowRightSLine />}
    </Link>
  );
};
