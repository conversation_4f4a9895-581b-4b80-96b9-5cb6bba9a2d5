import Modal from '@/components/Modal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { RiAlertLine } from '@remixicon/react';
import { Box } from '../Box';

interface IWarningPushAudience {
  open: boolean;
  setOpen: (open: boolean) => void;
  count?: number;
  submit?: () => void;
}

export const WarningPushAudience: React.FC<IWarningPushAudience> = (
  props: IWarningPushAudience,
) => {
  const { open, setOpen, count, submit } = props;
  const { t } = useTranslation();

  return (
    <Modal
      openModal={open}
      onCloseModal={setOpen}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="w-[430px]"
      titleAlign={'center'}
      title={
        <>
          <div className="m-auto w-fit">
            <RiAlertLine size={80} color={'#FFBC00'} />
          </div>
        </>
      }
    >
      <div className="flex flex-col items-center gap-4 p-4">
        <p className="text-center text-sm text-secondary max-w-md">
          {t('audience.warningMessagePushToTiktok', {
            count: count ?? 0,
            countTime: !!count && count > 1 ? 's' : '',
          })}
        </p>
        <Box className="gap-2">
          <Button
            onClick={() => setOpen(false)}
            className="px-6 py-2 rounded-xl w-[114px]"
            variant={'outline'}
          >
            {t('common.button.cancel')}
          </Button>
          <Button
            onClick={() => {
              setOpen(false);
              if (submit) {
                submit();
              }
            }}
            className="px-6 py-2 rounded-xl w-[114px]"
            variant={'primary'}
          >
            {t('common.button.ok')}
          </Button>
        </Box>
      </div>
    </Modal>
  );
};
